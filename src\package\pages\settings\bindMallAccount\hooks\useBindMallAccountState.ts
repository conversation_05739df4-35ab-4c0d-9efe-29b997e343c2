import { useRouter } from '@tarojs/taro';
import { useCallback, useEffect, useState } from 'react';

import useCountdown from '@/hooks/use-countdown';
import { isPhoneNumber } from '@/utils/common';

/**
 * useBindMallAccountState Hook 返回值类型定义
 */
export interface UseBindMallAccountStateReturn {
  // 基础状态
  phone: string;
  code: string;
  isBound: boolean;
  codeSent: boolean;

  // 派生状态
  isPhoneValid: boolean;
  isCodeInputEnabled: boolean;
  isBindButtonEnabled: boolean;

  // 倒计时相关
  count: number;
  isRunning: boolean;
  startCountdown: () => void;

  // 状态更新函数
  setPhone: (phone: string) => void;
  setCode: (code: string) => void;
  setCodeSentStatus: (sent: boolean) => void;
  setBoundStatus: (bound: boolean) => void;
  clearFormData: () => void;
  switchToBindPage: () => void;
}

/**
 * 绑定商城账户状态管理Hook
 *
 * @description 负责管理绑定商城账户页面的所有状态逻辑，包括表单状态、验证逻辑、倒计时功能等。
 * 遵循状态与逻辑分离原则，将组件的状态管理逻辑提取到独立的Hook中。
 *
 * @returns {UseBindMallAccountStateReturn} 返回包含状态和操作方法的对象，具体包含：
 * - **基础状态**
 *   - `phone` {string} - 用户输入的手机号码
 *   - `code` {string} - 用户输入的验证码
 *   - `isBound` {boolean} - 当前账户是否已绑定商城账户
 *   - `codeSent` {boolean} - 验证码是否已发送
 * - **派生状态**
 *   - `isPhoneValid` {boolean} - 手机号格式是否有效（符合11位数字规则）
 *   - `isCodeInputEnabled` {boolean} - 验证码输入框是否启用（验证码已发送时启用）
 *   - `isBindButtonEnabled` {boolean} - 绑定按钮是否启用（手机号有效且验证码为6位时启用）
 * - **倒计时相关**
 *   - `count` {number} - 倒计时剩余秒数
 *   - `isRunning` {boolean} - 倒计时是否正在运行
 *   - `startCountdown` {() => void} - 启动倒计时的方法
 * - **状态更新函数**
 *   - `setPhone` {(phone: string) => void} - 设置手机号的方法
 *   - `setCode` {(code: string) => void} - 设置验证码的方法
 *   - `setCodeSentStatus` {(sent: boolean) => void} - 设置验证码发送状态的方法
 *   - `setBoundStatus` {(bound: boolean) => void} - 设置绑定状态的方法
 *   - `clearFormData` {() => void} - 清空表单数据的方法
 *   - `switchToBindPage` {() => void} - 切换到绑定页面的方法
 *
 * @example
 * ```typescript
 * // 基本用法
 * const {
 *   phone,
 *   code,
 *   isPhoneValid,
 *   isBindButtonEnabled,
 *   setPhone,
 *   setCode,
 *   startCountdown
 * } = useBindMallAccountState();
 *
 * // 处理手机号输入
 * const handlePhoneChange = (value: string) => {
 *   setPhone(value);
 * };
 *
 * // 发送验证码
 * const handleSendCode = () => {
 *   if (isPhoneValid) {
 *     startCountdown();
 *     // 调用发送验证码API
 *   }
 * };
 * ```
 */
export const useBindMallAccountState = (): UseBindMallAccountStateReturn => {
  // 基础状态
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [isBound, setIsBound] = useState(false);
  const [codeSent, setCodeSent] = useState(false);

  // 路由参数处理
  const router = useRouter();

  // 倒计时Hook
  const { count, start, isRunning, clear } = useCountdown(60);

  // 初始化绑定状态
  useEffect(() => {
    const { isBound } = router.params;
    setIsBound(isBound === 'true');
  }, []);

  // 计算派生状态
  const isPhoneValid = isPhoneNumber(phone);
  const isCodeInputEnabled = codeSent;
  const isBindButtonEnabled = isPhoneValid && code.length === 6;

  /**
   * 清空表单数据
   * @description 重置手机号、验证码、发送状态，并清除倒计时
   * @returns {void}
   */
  const clearFormData = useCallback(() => {
    setPhone('');
    setCode('');
    setCodeSent(false);
    clear();
  }, [clear]);

  /**
   * 切换到绑定页面
   * @description 清空表单数据并设置绑定状态为false，用于从已绑定状态切换回绑定页面
   * @returns {void}
   */
  const switchToBindPage = useCallback(() => {
    clearFormData();
    setIsBound(false);
  }, [clearFormData]);

  /**
   * 设置验证码已发送状态
   * @description 更新验证码发送状态，用于控制验证码输入框的启用状态
   * @param {boolean} sent - 验证码是否已发送
   * @returns {void}
   */
  const setCodeSentStatus = useCallback((sent: boolean) => {
    setCodeSent(sent);
  }, []);

  /**
   * 设置绑定状态
   * @description 更新账户绑定状态，如果绑定成功则自动清空表单数据
   * @param {boolean} bound - 是否已绑定商城账户
   * @returns {void}
   */
  const setBoundStatus = useCallback(
    (bound: boolean) => {
      setIsBound(bound);
      if (bound) {
        clearFormData();
      }
    },
    [clearFormData],
  );

  return {
    // 基础状态
    phone,
    code,
    isBound,
    codeSent,

    // 派生状态
    isPhoneValid,
    isCodeInputEnabled,
    isBindButtonEnabled,

    // 倒计时相关
    count,
    isRunning,
    startCountdown: start,

    // 状态更新函数
    setPhone,
    setCode,
    setCodeSentStatus,
    setBoundStatus,
    clearFormData,
    switchToBindPage,
  };
};
