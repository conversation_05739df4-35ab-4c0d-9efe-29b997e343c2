import openai
import litellm

client = openai.OpenAI(
	api_key="sk-Ug4poFJUeBkPiVC6puFx1w",
	base_url="http://127.0.0.1:4000"
)

# request sent to model set on litellm proxy, `litellm --model`
response = client.chat.completions.create(
	model="gemini-2.5-pro",
	messages = [
    {
        "role": "user",
        "content": "hello"
    },
    {
        "role": "assistant",
        "content": "Hello there! How can I help you today?"
    }
]
)

print(response)
