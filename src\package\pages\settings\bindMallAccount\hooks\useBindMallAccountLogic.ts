import Taro from '@tarojs/taro';
import { useCallback } from 'react';

/**
 * useBindMallAccountLogic Hook 返回值类型定义
 * @interface UseBindMallAccountLogicReturn
 */
interface UseBindMallAccountLogicReturn {
  /** 发送验证码的方法 */
  sendVerificationCode: (
    phone: string,
    isPhoneValid: boolean,
    isRunning: boolean,
    onSuccess?: () => void,
    onError?: (error: any) => void,
  ) => Promise<void>;
  /** 绑定商城账户的方法 */
  bindMallAccount: (
    phone: string,
    code: string,
    isBindButtonEnabled: boolean,
    onSuccess?: () => void,
    onError?: (error: any) => void,
  ) => Promise<void>;
  /** 获取已绑定账户信息的方法 */
  getBoundAccountInfo: () => {
    phone: string;
    accountId: string;
    bindTime: string;
  };
}

/**
 * 绑定商城账户业务逻辑Hook
 *
 * @description 负责处理绑定商城账户相关的业务逻辑，包括发送验证码、绑定账户、获取账户信息等操作。
 * 遵循状态与逻辑分离原则，专门处理业务逻辑，与 useBindMallAccountState Hook 配合使用。
 *
 * @returns {UseBindMallAccountLogicReturn} 返回包含业务操作方法的对象，具体包含：
 * - **验证码相关**
 *   - `sendVerificationCode` {(phone: string, isPhoneValid: boolean, isRunning: boolean, onSuccess?: () => void, onError?: (error: any) => void) => Promise<void>} - 发送验证码的异步方法
 * - **账户绑定相关**
 *   - `bindMallAccount` {(phone: string, code: string, isBindButtonEnabled: boolean, onSuccess?: () => void, onError?: (error: any) => void) => Promise<void>} - 绑定商城账户的异步方法
 * - **信息获取相关**
 *   - `getBoundAccountInfo` {() => {phone: string, accountId: string, bindTime: string}} - 获取已绑定账户信息的方法
 *
 * @example
 * ```typescript
 * // 与状态Hook配合使用
 * const state = useBindMallAccountState();
 * const logic = useBindMallAccountLogic();
 *
 * // 发送验证码
 * const handleSendCode = async () => {
 *   await logic.sendVerificationCode(
 *     state.phone,
 *     state.isPhoneValid,
 *     state.isRunning,
 *     () => {
 *       state.setCodeSentStatus(true);
 *       state.startCountdown();
 *     },
 *     (error) => console.error('发送失败:', error)
 *   );
 * };
 *
 * // 绑定账户
 * const handleBind = async () => {
 *   await logic.bindMallAccount(
 *     state.phone,
 *     state.code,
 *     state.isBindButtonEnabled,
 *     () => state.setBoundStatus(true),
 *     (error) => console.error('绑定失败:', error)
 *   );
 * };
 *
 * // 获取账户信息
 * const accountInfo = logic.getBoundAccountInfo();
 * ```
 */
export const useBindMallAccountLogic = (): UseBindMallAccountLogicReturn => {
  /**
   * 发送验证码
   * @description 向指定手机号发送验证码，包含前置条件验证、API调用、错误处理和用户提示
   * @param {string} phone - 目标手机号码
   * @param {boolean} isPhoneValid - 手机号格式是否有效
   * @param {boolean} isRunning - 倒计时是否正在运行（防止重复发送）
   * @param {() => void} [onSuccess] - 发送成功的回调函数
   * @param {(error: any) => void} [onError] - 发送失败的回调函数
   * @returns {Promise<void>} 异步操作Promise
   */
  const sendVerificationCode = useCallback(
    async (
      phone: string,
      isPhoneValid: boolean,
      isRunning: boolean,
      onSuccess?: () => void,
      onError?: (error: any) => void,
    ) => {
      if (!isPhoneValid || isRunning) {
        return;
      }

      try {
        // TODO: 调用发送验证码API
        console.log('发送验证码到:', phone);

        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 1000));

        Taro.showToast({
          title: '验证码已发送',
          icon: 'success',
          duration: 2000,
        });

        onSuccess?.();
      } catch (error) {
        console.error('发送验证码失败:', error);

        Taro.showToast({
          title: '发送失败，请重试',
          icon: 'none',
          duration: 2000,
        });

        onError?.(error);
      }
    },
    [],
  );

  /**
   * 绑定商城账户
   * @description 使用手机号和验证码绑定商城账户，包含前置条件验证、API调用、错误处理和用户提示
   * @param {string} phone - 要绑定的手机号码
   * @param {string} code - 验证码
   * @param {boolean} isBindButtonEnabled - 绑定按钮是否可用（前置条件检查）
   * @param {() => void} [onSuccess] - 绑定成功的回调函数
   * @param {(error: any) => void} [onError] - 绑定失败的回调函数
   * @returns {Promise<void>} 异步操作Promise
   */
  const bindMallAccount = useCallback(
    async (
      phone: string,
      code: string,
      isBindButtonEnabled: boolean,
      onSuccess?: () => void,
      onError?: (error: any) => void,
    ) => {
      if (!isBindButtonEnabled) {
        return;
      }

      try {
        console.log('bindMallAccount', { phone, code });

        // TODO: 调用绑定商城账户API
        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 1000));

        Taro.showToast({
          title: '绑定成功',
          icon: 'success',
          duration: 2000,
        });

        onSuccess?.();
      } catch (error) {
        console.error('绑定商城账户失败:', error);

        Taro.showToast({
          title: '绑定失败，请重试',
          icon: 'none',
          duration: 2000,
        });

        onError?.(error);
      }
    },
    [],
  );

  /**
   * 获取已绑定的账户信息
   * @description 获取当前用户已绑定的商城账户详细信息，用于展示绑定状态页面
   * @todo 实际项目中应该从API获取真实数据
   * @returns {{phone: string, accountId: string, bindTime: string}} 返回包含账户信息的对象：
   * - `phone` {string} - 绑定的手机号码
   * - `accountId` {string} - 商城账户ID
   * - `bindTime` {string} - 绑定时间（格式：YYYY/MM/DD HH:mm:ss）
   */
  const getBoundAccountInfo = useCallback(() => {
    return {
      phone: '************',
      accountId: '********',
      bindTime: '2025/07/10 12:01:01',
    };
  }, []);

  return {
    sendVerificationCode,
    bindMallAccount,
    getBoundAccountInfo,
  };
};
